<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import { Button } from '@/components/ui/button';
import {
    CalendarCell,
    CalendarCellTrigger,
    CalendarGrid,
    CalendarGridBody,
    CalendarGridHead,
    CalendarGridRow,
    CalendarHeadCell,
    CalendarHeader,
    CalendarHeading,
} from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { type DateValue, getLocalTimeZone, today } from '@internationalized/date';
import { useVModel } from '@vueuse/core';
import { CalendarRoot, type CalendarRootEmits, type CalendarRootProps, useDateFormatter, useForwardPropsEmits } from 'reka-ui';
import { createDecade, createYear, toDate } from 'reka-ui/date';
import { computed, type HTMLAttributes, type Ref, ref, watch } from 'vue';

const props = withDefaults(
    defineProps<
        CalendarRootProps & {
            class?: HTMLAttributes['class'];
            error?: string;
            placeholderLabel?: string;
            modelValue: DateValue;
            disableFuture?: boolean;
            disableBeforeToday?: boolean;
        }
    >(),
    {
        placeholder() {
            return today(getLocalTimeZone());
        },
        weekdayFormat: 'short',
        disableFuture: false,
        disableBeforeToday: false,
    },
);
const emits = defineEmits<CalendarRootEmits>();
const isOpen = ref(false);
const delegatedProps = computed(() => {
    const { class: _, placeholder: __, ...delegated } = props;

    return delegated;
});
const todayDate = today(getLocalTimeZone());

const placeholder = useVModel(props, 'modelValue', emits, {
    passive: true,
    defaultValue: todayDate,
}) as Ref<DateValue>;

const forwarded = useForwardPropsEmits(delegatedProps, emits);

const formatter = useDateFormatter('en');
const hasSelected = ref(false);

const displayLabel = computed(() => {
    if (!hasSelected.value && props.placeholderLabel) {
        return props.placeholderLabel;
    }

    return placeholder;
});

watch(placeholder, (val) => {
    if (val) {
        hasSelected.value = true;
        isOpen.value = false;
    }
});

const isDateDisabled = (date: DateValue) => {
    const isFutureDisabled = props.disableFuture && date.compare(todayDate) > 0;
    const isPastDisabled = props.disableBeforeToday && date.compare(todayDate) < 0;
    return isFutureDisabled || isPastDisabled;
};
</script>

<template>
    <Popover v-model:open="isOpen">
        <PopoverTrigger as-child>
            <Button
                variant="outline"
                :class="
                    cn(
                        'w-full justify-between bg-white text-left font-normal',
                        props.placeholderLabel && 'text-muted-foreground',
                        props.error && 'border-red-500',
                    )
                "
            >
                {{ displayLabel }}
                <FaIcon name="calendar" />
            </Button>
        </PopoverTrigger>
        <PopoverContent class="w-auto p-0">
            <CalendarRoot
                v-slot="{ date, grid, weekDays }"
                v-model:modelValue="placeholder"
                v-bind="forwarded"
                :class="cn('rounded-md border p-3', props.class)"
            >
                <CalendarHeader>
                    <CalendarHeading class="flex w-full items-center justify-between gap-2">
                        <Select
                            :default-value="placeholder ? placeholder.month.toString() : ''"
                            @update:model-value="
                                (v) => {
                                    if (!v || !placeholder) return;
                                    if (Number(v) === placeholder?.month) return;
                                    placeholder = placeholder.set({
                                        month: Number(v),
                                    });
                                }
                            "
                        >
                            <SelectTrigger aria-label="Month" class="w-[60%]">
                                <SelectValue placeholder="Month" />
                            </SelectTrigger>
                            <SelectContent class="max-h-[200px]">
                                <SelectItem v-for="month in createYear({ dateObj: date })" :key="month.toString()" :value="month.month.toString()">
                                    {{ formatter.custom(toDate(month), { month: 'long' }) }}
                                </SelectItem>
                            </SelectContent>
                        </Select>

                        <Select
                            :default-value="placeholder ? placeholder.year.toString() : ''"
                            @update:model-value="
                                (v) => {
                                    if (!v || !placeholder) return;
                                    if (Number(v) === placeholder?.year) return;
                                    placeholder = placeholder.set({
                                        year: Number(v),
                                    });
                                }
                            "
                        >
                            <SelectTrigger aria-label="Year" class="w-[40%]">
                                <SelectValue placeholder="Year" />
                            </SelectTrigger>
                            <SelectContent class="max-h-[200px]">
                                <SelectItem
                                    v-for="yearValue in createDecade({ dateObj: date, startIndex: -120, endIndex: 120 }).filter(
                                        (y) => y.year <= new Date().getFullYear(),
                                    )"
                                    :key="yearValue.toString()"
                                    :value="yearValue.year.toString()"
                                >
                                    {{ yearValue.year }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </CalendarHeading>
                </CalendarHeader>

                <div class="flex flex-col space-y-4 pt-4 sm:flex-row sm:gap-x-4 sm:gap-y-0">
                    <CalendarGrid v-for="month in grid" :key="month.value.toString()">
                        <CalendarGridHead>
                            <CalendarGridRow>
                                <CalendarHeadCell v-for="day in weekDays" :key="day">
                                    {{ day }}
                                </CalendarHeadCell>
                            </CalendarGridRow>
                        </CalendarGridHead>
                        <CalendarGridBody class="grid">
                            <CalendarGridRow v-for="(weekDates, index) in month.rows" :key="`weekDate-${index}`" class="mt-2 w-full">
                                <CalendarCell
                                    v-for="weekDate in weekDates"
                                    :key="weekDate.toString()"
                                    :date="weekDate"
                                    :disabled="isDateDisabled(weekDate)"
                                >
                                    <CalendarCellTrigger :day="weekDate" :month="month.value" :disabled="isDateDisabled(weekDate)" />
                                </CalendarCell>
                            </CalendarGridRow>
                        </CalendarGridBody>
                    </CalendarGrid>
                </div>
            </CalendarRoot>
        </PopoverContent>
    </Popover>
</template>
