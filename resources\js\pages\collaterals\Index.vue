<script setup lang="ts">
import Calendar from '@/components/calendar/Calendar.vue';
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Selection {
    id: number;
    value: string;
}

interface Collateral {
    id: number;
    uuid: string;
    type: string;
    selection_type_id: number | null;
    type_selection: string | null;
    name: string;
    identity_no: string;
    status: number;
    status_label: string;
    created_at: string;
    updated_at: string;
    updated_by: {
        id: number;
        username: string;
    } | null;
    address: {
        line_1: string | null;
    } | null;
}

interface Props {
    collaterals: PaginatedData<Collateral>;
    collateralTypes: Selection[];
    filters: FilterOptions & {
        type?: string;
        name?: string;
        identity_no?: number;
        valuer?: string;
        valuation_received_date_from?: Date;
        valuation_received_date_to?: Date;
        land_search_received_date_from?: Date;
        land_search_received_date_to?: Date;
    };
    statuses: Record<string, string>;
}

const props = defineProps<Props>();

const form = useForm({});
const searchValue = ref(props.filters.name || '');
const collateralIdentityNo = ref(props.filters.identity_no || '');
const collateralType = ref(props.filters.type || 'All');
const valuer = ref(props.filters.valuer || '');
const valuerReceivedDateFrom = ref(props.filters.valuation_received_date_from || '');
const valuerReceivedDateTo = ref(props.filters.valuation_received_date_to || '');
const landSearchReceivedDateFrom = ref(props.filters.land_search_received_date_from || '');
const landSearchReceivedDateTo = ref(props.filters.land_search_received_date_to || '');
const allCollateralTypes = computed(() => [{ id: 'all', value: 'All' }, ...props.collateralTypes]);

const debouncedSearch = useDebounceFn(() => {
    form.get(
        route('collaterals.index', {
            name: searchValue.value,
            identity_no: collateralIdentityNo.value,
            type: collateralType.value !== 'All' ? collateralType.value : '',
            valuer: valuer.value,
            valuation_received_date_from: valuerReceivedDateFrom.value?.toString(),
            valuation_received_date_to: valuerReceivedDateTo.value?.toString(),
            land_search_received_date_from: landSearchReceivedDateFrom.value?.toString(),
            land_search_received_date_to: landSearchReceivedDateTo.value?.toString(),
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
}, 300);

const handleSearch = () => {
    debouncedSearch();
};
const handleReset = () => {
    searchValue.value = '';
    collateralIdentityNo.value = '';
    collateralType.value = 'All';
    valuer.value = '';
    valuerReceivedDateFrom.value = '';
    valuerReceivedDateTo.value = '';
    landSearchReceivedDateFrom.value = '';
    landSearchReceivedDateTo.value = '';

    form.get(
        route('collaterals.index', {
            ...props.filters,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));

const handleSort = (field: string) => {
    const column = columns.find((col) => col.field === field);
    const sortField = column?.sortKey || field;
    const direction = props.filters.sort_field === sortField && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';
    form.get(
        route('collaterals.index', {
            ...props.filters,
            sort_field: sortField,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleView = (collateral: Collateral) => {
    form.get(route('collaterals.show', collateral.id));
};

const handleEdit = (collateral: Collateral) => {
    form.get(route('collaterals.edit', collateral.id));
};

const handleDelete = (collateral: Collateral) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Are you sure you want to delete this collateral? This action cannot be undone.`,
        },
        submitOptions: {
            method: 'delete',
            url: route('collaterals.destroy', collateral.id),
            transform: (data) => ({
                ...data,
            }),
            successMessage: `Collateral deleted successfully.`,
            errorMessage: `Failed to delete collateral. Please try again.`,
            entityName: 'collateral',
        },
    });
};

const columns = [
    { field: 'id', label: 'Collateral No.', sortable: true },
    { field: 'type_selection', label: 'Collateral Type', sortable: true, sortKey: 'selection_type_id' },
    { field: 'name', label: 'Owner/Company Name', sortable: false },
    { field: 'identity_no', label: 'IC/Business Registration No.', sortable: false },
    { field: 'property.address.line_1', label: 'Collateral Address', sortable: false },
    { field: 'valuer.valuer', label: 'Valuer', sortable: false },
    {
        field: 'valuer.valuation_received_date',
        label: 'Valuation Received Date',
        sortable: false,
        format: (value) => formatDateTime(value),
    },
    {
        field: 'valuer.land_search_received_date',
        label: 'Land Search Received Date',
        sortable: false,
        format: (value) => (value == '-' ? '-' : formatDateTime(value)),
    },
    { field: 'updated_at', label: 'Updated Date', sortable: true, format: (value) => formatDateTime(value) },
    { field: 'updated_by.username', label: 'Updated By', sortable: true },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];
</script>

<template>
    <AppLayout>
        <Head title="Collaterals" />

        <div class="px-4 py-6">
            <Heading title="Collaterals" pageNumber="P000065" />
            <SearchCard
                v-model:searchValue="searchValue"
                searchLabel="Owner/Company Name"
                searchPlaceholder="Owner/Company Name"
                @search="handleSearch"
                @reset="handleReset"
            >
                <template #additional-filters>
                    <div class="w-full">
                        <Label class="pb-2" for="collateral-ic">IC/Business Registration No.</Label>
                        <Input id="collateral-ic" placeholder="IC/Business Registration No." v-model="collateralIdentityNo" class="w-full" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="type">Collateral Types</Label>
                        <Select :model-value="collateralType" @update:model-value="collateralType = String($event)">
                            <SelectTrigger class="w-full">
                                <SelectValue placeholder="Select Collateral Types" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="option in allCollateralTypes" :key="option.id" :value="option.value">
                                    {{ option.value }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="valuer">Valuer</Label>
                        <Input id="valuer" placeholder="Search Valuer..." v-model="valuer" class="w-full" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="valuer-received-date-from">Valuation Received Date (From)</Label>
                        <Calendar v-model="valuerReceivedDateFrom" placeholderLabel="Valuation Received Date (From)" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="valuer-received-date-to">Valuation Received Date (To)</Label>
                        <Calendar v-model="valuerReceivedDateTo" placeholderLabel="Valuation Received Date (To)" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="land-search-received-date-from">Land Search Received Date (From)</Label>
                        <Calendar v-model="landSearchReceivedDateFrom" placeholderLabel="Land Search Received Date (From)" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="land-search-received-date-to">Land Search Received Date (To)</Label>
                        <Calendar v-model="landSearchReceivedDateTo" placeholderLabel="Land Search Received Date (To)" />
                    </div>
                </template>
            </SearchCard>
            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('collaterals.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Collateral
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="collaterals.data"
                        :sort-state="sortState"
                        empty-message="No collaterals found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        @delete="handleDelete"
                        :showDeleteButton="true"
                        :showStatusToggle="false"
                    >
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="collaterals.from" :to="collaterals.to" :total="collaterals.total" entityName="collaterals" />
                            </div>
                            <Pagination :links="collaterals.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
