<script setup lang="ts">
import Calendar from '@/components/calendar/Calendar.vue';
import Collateral from '@/components/customer/Collateral.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed, onMounted, reactive, ref, watch } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Selection {
    id: number;
    value: string;
}

interface Customer {
    id: number;
    uuid: string;
    team: string;
    selection_type_id: number | null;
    type: string;
    type_selection: string | null;
    name: string;
    email: string;
    identity_no: string;
    old_identity_no: string;
    registration_date: string;
    years_of_incorporation: number;
    age: number;
    birth_date: string;
    selection_gender_id: number;
    selection_race_id: number;
    selection_nationality_id: number;
    selection_education_level_id: number;
    selection_marriage_status_id: number;
    employment: Employment | null;
    remark: string;
    documents: DocumentItem;
}

interface Employment {
    id: number | null;
    selection_terms_of_employment_id: number | null;
    length_service_year: string | null;
    length_service_month: string | null;
    employer_name: string | null;
    job_position: string | null;
    selection_occupation_id: number | null;
    selection_business_category_id: number | null;
    gross_income: string | null;
    net_income: string | null;
    address: Address | null;
}

interface PropertyOwner {
    id?: number;
    name: string;
    identity_no: string;
    selection_telephone_country_id: number | null;
    selection_mobile_country_id: number | null;
    telephone: string | null;
    mobile_phone: string | null;
    remark: string | null;
    address: Address | null;
    _delete?: boolean;
}

interface Address {
    id?: number;
    selection_type_id: number | null;
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    state: string | null;
    selection_state_id: number | null;
    country: string | null;
    selection_country_id: number | null;
}

interface Valuer {
    id?: number;
    valuation_amount: number;
    valuer: string;
    valuation_received_date: string | null;
    land_search_received_date: string | null;
    is_primary: boolean | null;
    _delete?: boolean;
}

interface Property {
    id?: number;
    ownership_no: string | null;
    lot_number: string | null;
    selection_land_category_id: number | null;
    land_category: string | null;
    land_category_selection: string | null;
    land_category_other: string | null;
    selection_type_of_property_id: number | null;
    type_of_property: string | null;
    type_of_property_selection: string | null;
    selection_land_size_unit: number | null;
    land_size: string | null;
    selection_land_status_id: number | null;
    land_status: string | null;
    land_status_selection: string | null;
    no_syit_piawai: string | null;
    certified_plan_no: string | null;
    selection_built_up_area_unit: number | null;
    built_up_area_of_property: string | null;
    city: string | null;
    location: string | null;
    district: string | null;
    address: Address | null;
}

interface Collateral {
    id: number;
    uuid: string;
    selection_customer_type_id: number | null;
    selection_type_id: number | null;
    type: string;
    type_selection: string | null;
    name: string;
    identity_no: string;
    company_name: string | null;
    business_registration_no: string | null;
    status: number;
    status_label: string;
    remark: string | null;
    property: Property | null;
    property_owners: PropertyOwner[];
    valuers: Valuer[];
}

type Owner = {
    selection_type_id: number | null;
    name: string;
    identity_no: string;
    selection_nationality_id: number | null;
};

interface Props {
    customer: Customer;
    customerTypes: Selection[];
    collateralTypes: Selection[];
    propertyTypes: Selection[];
    landCategories: Selection[];
    squareTypes: Selection[];
    landStatuses: Selection[];
    states: Selection[];
    countries: Selection[];
    contactTypes: Selection[];
    telephoneCountries: Selection[];
    mobileCountries: Selection[];
    ownerTypes: Selection[];
    addressTypes: Selection[];
    termsOfEmploymentTypes: Selection[];
    occupationsTypes: Selection[];
    nationalities: Selection[];
    genders: Selection[];
    educationTypes: Selection[];
    naturesOfBusinessTypes: Selection[];
    maritalStatus: Selection[];
    races: Selection[];
    businessClassificationTypes: Selection[];
    documentTypes: Selection[];
}

type DocumentItem = {
    selection_type_id: number | null;
    name: string;
    size: number;
    type: string;
    lastModified: number;
    url: string;
    base64?: string;
    createdAt?: any;
    uploadedBy?: string;
};

const props = defineProps<Props>();
const openAccordionIndex = ref<string | undefined>(undefined);
const ordinal = (n: number) => {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
};
const openAccordion = ref('0');
const PERSONAL_TYPE_ID = props.customerTypes.find((type) => type.value === 'Personal')?.id ?? null;
const SELF_EMPLOYED_TYPE_ID = props.termsOfEmploymentTypes.find((type) => type.value === 'Self-Employed')?.id ?? null;
const TELEPHONE_TYPE_ID = props.contactTypes.find((type) => type.value === 'Telephone')?.id ?? null;
const MOBILE_TYPE_ID = props.contactTypes.find((type) => type.value === 'Mobile Phone')?.id ?? null;
const CUSTOMER_DOC_ID = props.documentTypes.find((type) => type.value === 'Customer Documents')?.id ?? null;
const COLLATERAL_DOC_ID = props.documentTypes.find((type) => type.value === 'Collateral Documents')?.id ?? null;
const SECURITY_DOC_ID = props.documentTypes.find((type) => type.value === 'Security Documents')?.id ?? null;

const form = useForm({
    selection_type_id: props.customer.selection_type_id,
    team: props.customer.team,
    email: props.customer.email,
    remark: props.customer.remark,

    selection_race_id: props.customer.selection_race_id,
    selection_gender_id: props.customer.selection_gender_id,
    selection_marriage_status_id: props.customer.selection_marriage_status_id,
    selection_nationality_id: props.customer.selection_nationality_id,
    selection_education_level_id: props.customer.selection_education_level_id,

    // Business
    old_identity_no: props.customer.old_identity_no,
    registration_date: props.customer.registration_date,
    years_of_incorporation: props.customer.years_of_incorporation,

    contacts: props.customer.contacts.map((contact) => ({
        id: contact.id,
        selection_type_id: contact.selection_type_id,
        selection_telephone_country_id: contact.selection_country_id,
        selection_mobile_country_id: contact.selection_country_id,
        telephone: contact.selection_type_id == TELEPHONE_TYPE_ID ? contact.contact : null,
        mobile_phone: contact.selection_type_id == MOBILE_TYPE_ID ? contact.contact : null,
        can_receive_sms: contact.can_receive_sms,
        is_primary: contact.is_primary,
        _delete: false
    })),

    addresses: props.customer.addresses.map((address) => ({
        id: address.id,
        selection_type_id: address.selection_type_id,
        line_1: address.line_1,
        line_2: address.line_2,
        postcode: address.postcode,
        city: address.city,
        selection_state_id: address.selection_state_id,
        selection_country_id: address.selection_country_id,
        is_primary: address.is_primary,
        _delete: false
    })),

    employment: props.customer.employment
        ? {
            id: props.customer.employment.id,
            selection_terms_of_employment_id: props.customer.employment.selection_terms_of_employment_id,
            length_service_year: props.customer.employment.length_service_year,
            length_service_month: props.customer.employment.length_service_month,
            employer_name: props.customer.employment.employer_name,
            job_position: props.customer.employment.job_position,
            selection_occupation_id: props.customer.employment.selection_occupation_id,
            selection_business_category_id: props.customer.employment.selection_business_category_id,
            gross_income: props.customer.employment.gross_income,
            net_income: props.customer.employment.net_income,
            selection_telephone_country_id: props.customer.employment.selection_telephone_country_id,
            selection_mobile_country_id: props.customer.employment.selection_mobile_country_id,
            telephone: props.customer.employment.telephone,
            mobile_phone: props.customer.employment.mobile_phone,
            address: props.customer.employment.address
                ? {
                    id: props.customer.employment.address.id,
                    selection_type_id: props.customer.employment.address.selection_type_id,
                    line_1: props.customer.employment.address.line_1,
                    line_2: props.customer.employment.address.line_2,
                    postcode: props.customer.employment.address.postcode,
                    city: props.customer.employment.address.city,
                    selection_state_id: props.customer.employment.address.selection_state_id,
                    selection_country_id: props.customer.employment.address.selection_country_id
                }
                : null
        }
        : null,

    company: props.customer.company
        ? {
            id: props.customer.company.id,
            selection_nature_of_business_id: props.customer.company.selection_nature_of_business_id,
            selection_country_of_business_id: props.customer.company.selection_country_of_business_id,
            current_paid_up_capital: props.customer.company.current_paid_up_capital,
            business_turnover: props.customer.company.business_turnover,
            business_turnover_date: props.customer.company.business_turnover_date
                ? new Date(props.customer.company.business_turnover_date.replace(' ', 'T') + 'Z').toISOString()
                : null,
            business_net_income: props.customer.company.business_net_income,
            business_net_income_date: props.customer.company.business_net_income_date
                ? new Date(props.customer.company.business_net_income_date.replace(' ', 'T') + 'Z').toISOString()
                : null
        }
        : null,

    owners: props.customer.owners.map((owner) => ({
        id: owner.id,
        name: owner.name,
        identity_no: owner.identity_no,
        selection_type_id: owner.selection_type_id,
        selection_nationality_id: owner.selection_nationality_id,
        share_unit: owner.share_unit,
        _delete: false
    })),

    collateral: props.customer.collaterals.map((collateral) => ({
        id: collateral.id,
        selection_type_id: collateral.selection_type_id,
        selection_customer_type_id: collateral.selection_customer_type_id,
        typeSelection: collateral.typeSelection,
        name: collateral.name,
        identity_no: collateral.identity_no,
        remark: collateral.remark,
        _delete: false,
        property: collateral.property
            ? {
                id: collateral.property.id,
                ownership_no: collateral.property.ownership_no,
                lot_number: collateral.property.lot_number,
                selection_land_category_id: collateral.property.selection_land_category_id,
                land_category: collateral.property.land_category,
                land_category_other: collateral.property.land_category_other,
                selection_type_of_property_id: collateral.property.selection_type_of_property_id,
                type_of_property: collateral.property.type_of_property,
                selection_land_size_unit: collateral.property.selection_land_size_unit,
                land_size: collateral.property.land_size,
                selection_land_status_id: collateral.property.selection_land_status_id,
                land_status: collateral.property.land_status,
                no_syit_piawai: collateral.property.no_syit_piawai,
                certified_plan_no: collateral.property.certified_plan_no,
                selection_built_up_area_unit: collateral.property.selection_built_up_area_unit,
                built_up_area_of_property: collateral.property.built_up_area_of_property,
                city: collateral.property.city,
                location: collateral.property.location,
                district: collateral.property.district,
                address: collateral.property.address
                    ? {
                        id: collateral.property.address.id,
                        line_1: collateral.property.address.line_1,
                        line_2: collateral.property.address.line_2,
                        postcode: collateral.property.address.postcode,
                        city: collateral.property.address.city,
                        selection_state_id: collateral.property.address.selection_state_id,
                        state: collateral.property.address.state,
                        selection_country_id: collateral.property.address.selection_country_id,
                        country: collateral.property.address.country
                    }
                    : null
            }
            : null,
        property_owners: (collateral.owners ?? []).map((owner) => ({
            id: owner.id,
            name: owner.name,
            identity_no: owner.identity_no,
            selection_telephone_country_id: owner.selection_telephone_country_id ?? props.telephoneCountries[0]?.id ?? null,
            selection_mobile_country_id: owner.selection_mobile_country_id ?? props.mobileCountries[0]?.id ?? null,
            telephone: owner.telephone,
            mobile_phone: owner.mobile_phone,
            address: owner.address
                ? {
                    id: owner.address.id,
                    line_1: owner.address.line_1,
                    line_2: owner.address.line_2,
                    postcode: owner.address.postcode,
                    city: owner.address.city,
                    selection_state_id: owner.address.selection_state_id,
                    selection_country_id: owner.address.selection_country_id
                }
                : null,
            _delete: false
        })),
        valuers: collateral.valuers.map((valuer) => ({
            id: valuer.id,
            valuation_amount: valuer.valuation_amount,
            valuer: valuer.valuer,
            valuation_received_date: valuer.valuation_received_date,
            land_search_received_date: valuer.land_search_received_date,
            is_primary: valuer.is_primary,
            _delete: false
        }))
    })),

    document: [] as DocumentItem[],

    docDeleteItems: {} as Record<number, boolean>
});

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('customers.update', props.customer.id),
            entityName: 'customer'
        }
    });
};

const addContact = () => {
    form.contacts.push({
        selection_type_id: props.contactTypes[0]?.id,
        selection_telephone_country_id: null,
        selection_mobile_country_id: props.mobileCountries[0]?.id,
        telephone: null,
        mobile_phone: null,
        can_receive_sms: true,
        is_primary: false
    });
};

const removeContact = (index: number) => {
    if (form.contacts[index].id) {
        form.contacts[index]._delete = true;
    } else {
        form.contacts.splice(index, 1);
    }
};

const addAddress = () => {
    form.addresses.push({
        selection_type_id: props.addressTypes[0]?.id,
        line_1: null,
        line_2: null,
        postcode: null,
        city: null,
        selection_state_id: null,
        state: null,
        selection_country_id: null,
        country: null,
        is_primary: false
    });
    openAccordion.value = (form.addresses.length - 1).toString();
};

const removeAddress = (index: number) => {
    if (form.addresses[index].id) {
        form.addresses[index]._delete = true;
    } else {
        form.addresses.splice(index, 1);
    }
};

const addOwner = () => {
    form.owners.push({
        selection_type_id: props.ownerTypes[0]?.id,
        name: '',
        identity_no: '',
        selection_nationality_id: null,
        shareunit: 0
    });
    openAccordionIndex.value = (form.owners.length - 1).toString();
};

const removeOwner = (index: number) => {
    if (form.owners[index].id) {
        form.owners[index]._delete = true;
    } else {
        form.owners.splice(index, 1);
    }
};

const addCollateral = () => {
    form.collateral.push({
        selection_customer_type_id: form.selection_type_id,
        selection_type_id: props.collateralTypes[0]?.id ?? null,
        name: '',
        identity_no: '',
        status: 0,
        remark: null,
        company_name: form.selection_type_id !== PERSONAL_TYPE_ID ? props.customer.name : null,
        business_registration_no: form.selection_type_id !== PERSONAL_TYPE_ID ? props.customer.identity_no : null,

        // Property data
        property: {
            ownership_no: null,
            lot_number: null,
            selection_land_category_id: null,
            land_category: null,
            land_category_other: null,
            selection_type_of_property_id: null,
            type_of_property: null,
            selection_land_size_unit: props.squareTypes[0]?.id,
            land_size: null,
            selection_land_status_id: null,
            land_status: null,
            no_syit_piawai: null,
            certified_plan_no: null,
            selection_built_up_area_unit: props.squareTypes[0]?.id,
            built_up_area_of_property: null,
            city: null,
            location: null,
            district: null,
            address: {
                line_1: null,
                line_2: null,
                postcode: null,
                city: null,
                selection_state_id: null,
                state: null,
                selection_country_id: null,
                country: null
            }
        },

        // Property owners
        property_owners: [],

        // Valuers
        valuers: [
            {
                valuation_amount: 0,
                valuer: '',
                valuation_received_date: null as string | null,
                land_search_received_date: null as string | null,
                is_primary: true as boolean | null
            }
        ]
    });
};

const removeCollateral = (index: number) => {
    if (form.collateral[index].id) {
        form.collateral[index]._delete = true;
    } else {
        form.collateral.splice(index, 1);
    }
};

const isSelfEmployed = computed(() => {
    return form.employment?.selection_terms_of_employment_id === SELF_EMPLOYED_TYPE_ID;
});

const activeTab = ref('personal');

const tabFlow = computed(() => ({
    personal: props.customer.selection_type_id === PERSONAL_TYPE_ID ? 'employment' : 'company',
    employment: 'collateral',
    company: 'collateral',
    collateral: 'document',
    document: null
}));

const tabBackFlow = computed(() => ({
    employment: 'personal',
    company: 'personal',
    collateral: form.selection_type_id === PERSONAL_TYPE_ID ? 'employment' : 'company',
    document: 'collateral'
}));

const tabItems = computed(() => [
    { label: 'Personal Detail', value: 'personal' },
    ...(props.customer.selection_type_id === PERSONAL_TYPE_ID
        ? [{ label: 'Employment Details', value: 'employment' }]
        : [{ label: 'Company Details', value: 'company' }]),
    { label: 'Collateral', value: 'collateral' },
    { label: 'Document', value: 'document' }
]);

const docItems = computed(() => [
    { label: 'All', value: 'all' },
    { label: 'Customer Documents', value: 'customer-doc' },
    { label: 'Collateral Documents', value: 'collateral-doc' },
    { label: 'Security Documents', value: 'security-doc' }
]);

const goToNextTab = () => {
    const current = activeTab.value as keyof typeof tabFlow.value;
    const next = tabFlow.value[current];
    if (next) activeTab.value = next;
};

const goToPreviousTab = () => {
    const current = activeTab.value as keyof typeof tabBackFlow.value;
    const prev = tabBackFlow.value[current];
    if (prev) activeTab.value = prev;
};

watch(
    () => form.company?.business_turnover_date,
    (date) => {
        if (form.company && typeof date === 'string') {
            form.company.business_turnover_date = date?.toString();
        }
    }
);

watch(
    () => form.company?.business_net_income_date,
    (date) => {
        if (form.company && typeof date === 'string') {
            form.company.business_net_income_date = date?.toString();
        }
    }
);

watch(
    () => form.registration_date,
    (date) => {
        if (date) {
            const regYear = new Date(date).getFullYear();
            const currentYear = new Date().getFullYear();
            form.years_of_incorporation = currentYear - regYear;

            // Optional: format date to YYYY-MM-DD
            form.registration_date = date?.toString();
        } else {
            form.years_of_incorporation = null;
            form.registration_date = null;
        }
    }
);

const activeDocTab = ref('all');
const uploadedFiles = ref<
    {
        file: File;
        selection_type_id: number | null;
    }[]
>([]);

function removeFile(index: number) {
    uploadedFiles.value.splice(index, 1);
}

function viewFile(file: File) {
    const fileURL = URL.createObjectURL(file);
    window.open(fileURL, '_blank');
}

type FileData = {
    id?: number | string;
    name: string;
    size: number;
    url: string;
    typeId: number;
    type: string;
    isExisting: boolean;
    createdAt: string;
    uploadedBy: string;
};

type TabKey = 'customer-doc' | 'collateral-doc' | 'security-doc' | 'all';
const selectedDocIds = ref<(number | string | undefined)[]>([]);
const allDocs = computed(() => {
    return Object.values(categorizedFiles).flat(); // includes all docs from all tabs
});

const selectedFile = computed(() => {
    const id = selectedDocIds.value[0];
    return allDocs.value.find((doc) => doc.id === id) || null;
});
const toggleSelect = (docId: string | number | undefined) => {
    const i = selectedDocIds.value.indexOf(docId);
    if (i === -1) {
        selectedDocIds.value.push(docId);
    } else {
        selectedDocIds.value.splice(i, 1);
    }
};

const categorizedFiles = reactive<Record<TabKey, FileData[]>>({
    'customer-doc': [],
    'collateral-doc': [],
    'security-doc': [],
    all: []
});

const typeToTabKey: Record<number, TabKey> = {};

if (CUSTOMER_DOC_ID) typeToTabKey[CUSTOMER_DOC_ID] = 'customer-doc';
if (COLLATERAL_DOC_ID) typeToTabKey[COLLATERAL_DOC_ID] = 'collateral-doc';
if (SECURITY_DOC_ID) typeToTabKey[SECURITY_DOC_ID] = 'security-doc';

const handleFileUpload = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files) {
        const newFiles = Array.from(target.files);

        const existingFileNames = uploadedFiles.value.map((f) => f.file.name);
        const uniqueNewFiles = newFiles.filter((f) => !existingFileNames.includes(f.name));

        uniqueNewFiles.forEach((file) => {
            uploadedFiles.value.push({
                file,
                selection_type_id: null
            });
        });

        target.value = '';
    }
};

const getPreviewUrl = (file: File & { previewUrl?: string }) => {
    if (!file.previewUrl) {
        file.previewUrl = URL.createObjectURL(file);
    }
    return file.previewUrl;
};

function fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

const uploadFiles = async () => {
    let nextId = (props.customer.documents?.length || 0) + 1;
    for (const { file, selection_type_id } of uploadedFiles.value) {
        if (!selection_type_id || !(file instanceof File)) continue;

        const url = URL.createObjectURL(file);
        const base64File = await fileToBase64(file);

        const doc: DocumentItem & { base64: string } = {
            selection_type_id,
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified,
            url,
            base64: base64File,
            createdAt: new Date(file.lastModified).toLocaleString()
        };

        form.document.push(doc);
        const tempId = `temp-${nextId++}`;
        const fileEntry = {
            id: tempId, // for new uploads
            name: file.name,
            size: file.size,
            type: doc.type,
            url,
            typeId: selection_type_id,
            isExisting: false,
            createdAt: doc.createdAt,
            uploadedBy: '-'
        };

        // Always push to 'all'
        categorizedFiles['all'].push(fileEntry);

        // Also push to specific tab if typeId matches
        const tabKey = typeToTabKey[selection_type_id];
        if (tabKey) {
            categorizedFiles[tabKey].push(fileEntry);
        }
    }

    uploadedFiles.value = [];
};

onMounted(() => {
    if (props.customer.documents) {
        props.customer.documents.forEach((doc) => {
            const baseStorageUrl = `${window.location.origin}/storage/`;
            const rawPath = doc.file?.url || doc.url;
            const fullUrl = rawPath.startsWith('http') ? rawPath : baseStorageUrl + rawPath;

            const fileName = doc.file?.name || rawPath.split('/').pop();
            const extension = fileName?.split('.').pop()?.toLowerCase();

            let type = 'Other';
            if (extension === 'pdf') {
                type = 'PDF';
            } else if (['jpg', 'jpeg'].includes(extension)) {
                type = 'JPG';
            } else if (extension === 'png') {
                type = 'PNG';
            }

            const fileEntry = {
                id: doc.id,
                name: fileName,
                url: fullUrl,
                type,
                size: doc.file?.size,
                typeId: doc.selection_type_id,
                isExisting: true,
                createdAt: new Date(doc.created_at).toLocaleString(),
                uploadedBy: doc['uploaded_by']?.username
            };

            // Push to 'all'
            categorizedFiles['all'].push(fileEntry);

            // Push to specific tab if valid
            const tabKey = typeToTabKey[doc.selection_type_id];
            if (tabKey) {
                categorizedFiles[tabKey].push(fileEntry);
            }
        });
    }
});

const isImage = (fileName: string): boolean => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ['jpg', 'jpeg', 'png'].includes(ext ?? '');
};

const removeSelectedDocs = () => {
    const toRemoveIds = [...selectedDocIds.value];

    toRemoveIds.forEach((docId) => {
        // Find the document in 'all'
        const doc = categorizedFiles['all'].find((d) => d.id === docId);
        if (!doc) return;

        // If it's an existing document, mark for deletion
        if (doc.isExisting && doc.id) {
            form.docDeleteItems[doc.id] = true;
        } else {
            // For new (non-existing) documents, remove from form.document
            const i = form.document.findIndex((d) => d.name === doc.name && d.url === doc.url);
            if (i !== -1) form.document.splice(i, 1);
        }

        // Remove from all tabs
        (Object.keys(categorizedFiles) as Array<keyof typeof categorizedFiles>).forEach((tabKey) => {
            const tabArray = categorizedFiles[tabKey];
            const indexInTab = tabArray.findIndex((d) => d.id === docId);
            if (indexInTab !== -1) tabArray.splice(indexInTab, 1);
        });
    });

    // Clear selection
    selectedDocIds.value = [];
};

function formatDate(dateString: string | null) {
    if (!dateString) return '-';
    return new Date(dateString).toISOString().slice(0, 10); // Formats as 'YYYY-MM-DD'
}
</script>

<template>
    <AppLayout>
        <Head title="Edit Customer" />
        <div class="px-4 py-3">
            <Heading title="Customer" pageNumber="P000003" description="Edit the selected customer record" />

            <form @submit.prevent="submit">
                <Card class="gap-0 py-0">
                    <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                        <CardTitle>Edit Customer</CardTitle>
                    </CardHeader>
                    <TabsWrapper v-model="activeTab" :tabs="tabItems">
                        <template #personal>
                            <CardContent class="py-4">
                                <Label class="text-[20px]">Personal Details</Label>
                                <div class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                                    <template v-if="props.customer.selection_type_id === PERSONAL_TYPE_ID">
                                        <div>
                                            <Label for="customer-type:" class="text-base"
                                            >Cutomer Type
                                                <RequiredIndicator />
                                            </Label>
                                            <p>
                                                {{ props.customerTypes.find((type) => type.id === props.customer.selection_type_id)?.value
                                                }}
                                            </p>
                                        </div>
                                        <div>
                                            <Label for="team"
                                            >Team
                                                <RequiredIndicator />
                                            </Label>
                                            <p>{{ props.customer.team }}</p>
                                        </div>
                                        <div>
                                            <Label for="name" class="text-base"
                                            >Name
                                                <RequiredIndicator />
                                            </Label>
                                            {{ props.customer.name }}
                                        </div>
                                        <div>
                                            <Label for="identity_no" class="text-base"
                                            >Identity No
                                                <RequiredIndicator />
                                            </Label>
                                            {{ props.customer.identity_no }}
                                        </div>
                                        <div>
                                            <Label for="dob" class="text-base"
                                            >Date of Birth
                                                <RequiredIndicator />
                                            </Label>
                                            {{ formatDate(props.customer.birth_date) }}
                                        </div>
                                        <div>
                                            <Label for="age" class="text-base"
                                            >Age
                                                <RequiredIndicator />
                                            </Label>
                                            {{ props.customer.age }}
                                        </div>
                                        <div>
                                            <Label for="race" class="text-base"
                                            >Race
                                                <RequiredIndicator />
                                            </Label>
                                            <Select v-model="form.selection_race_id"
                                                    :error="form.errors['selection_race_id']">
                                                <SelectTrigger class="w-full">
                                                    <SelectValue placeholder="Select Race" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="type in races" :key="type.id" :value="type.id">
                                                        {{ type.value }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <div v-if="form.errors['selection_race_id']"
                                                 class="mt-1 text-sm text-red-500">
                                                {{ form.errors['selection_race_id'] }}
                                            </div>
                                        </div>
                                        <div>
                                            <Label for="gender" class="text-base"
                                            >Gender
                                                <RequiredIndicator />
                                            </Label>
                                            <Select v-model="form.selection_gender_id"
                                                    :error="form.errors['selection_gender_id']">
                                                <SelectTrigger class="w-full">
                                                    <SelectValue placeholder="Select Gender" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="type in genders" :key="type.id" :value="type.id">
                                                        {{ type.value }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <div v-if="form.errors['selection_gender_id']"
                                                 class="mt-1 text-sm text-red-500">
                                                {{ form.errors['selection_gender_id'] }}
                                            </div>
                                        </div>
                                        <div>
                                            <Label for="marital_status" class="text-base"
                                            >Marital Status
                                                <RequiredIndicator />
                                            </Label>
                                            <Select v-model="form.selection_marriage_status_id"
                                                    :error="form.errors['selection_marriage_status_id']">
                                                <SelectTrigger class="w-full">
                                                    <SelectValue placeholder="Select Marital Status" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="type in maritalStatus" :key="type.id"
                                                                :value="type.id">
                                                        {{ type.value }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <div v-if="form.errors['selection_marriage_status_id']"
                                                 class="mt-1 text-sm text-red-500">
                                                {{ form.errors['selection_marriage_status_id'] }}
                                            </div>
                                        </div>
                                        <div>
                                            <Label for="nationality" class="text-base"
                                            >Nationality
                                                <RequiredIndicator />
                                            </Label>
                                            <Select v-model="form.selection_nationality_id"
                                                    :error="form.errors['selection_nationality_id']">
                                                <SelectTrigger class="w-full">
                                                    <SelectValue placeholder="Select Nationality" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="type in nationalities" :key="type.id"
                                                                :value="type.id">
                                                        {{ type.value }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <div v-if="form.errors['selection_nationality_id']"
                                                 class="mt-1 text-sm text-red-500">
                                                {{ form.errors['selection_nationality_id'] }}
                                            </div>
                                        </div>
                                        <div>
                                            <Label for="education_level" class="text-base"
                                            >Education Level
                                                <RequiredIndicator />
                                            </Label>
                                            <Select v-model="form.selection_education_level_id"
                                                    :error="form.errors.selection_education_level_id">
                                                <SelectTrigger class="w-full">
                                                    <SelectValue placeholder="Select Education Level" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="type in educationTypes" :key="type.id"
                                                                :value="type.id">
                                                        {{ type.value }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <div v-if="form.errors['selection_education_level_id']"
                                                 class="mt-1 text-sm text-red-500">
                                                {{ form.errors['selection_education_level_id'] }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div class="col-span-2">
                                            <Label for="team" class="text-base">Team</Label>
                                            <p>{{ props.customer.team }}</p>
                                        </div>
                                        <div>
                                            <Label for="customer-type:" class="text-base">Cutomer Type</Label>
                                            <p>
                                                {{ props.customerTypes.find((type) => type.id === props.customer.selection_type_id)?.value
                                                }}
                                            </p>
                                        </div>
                                        <div>
                                            <Label for="name" class="text-base"
                                            >Name
                                                <RequiredIndicator />
                                            </Label>
                                            {{ props.customer.name }}
                                        </div>
                                        <div>
                                            <Label for="identity_no" class="text-base"
                                            >New Business Registration No.
                                                <RequiredIndicator />
                                            </Label>
                                            {{ props.customer.identity_no }}
                                        </div>
                                        <div>
                                            <Label for="old_business_registration_no" class="text-base"
                                            >Old Business Registration No.
                                                <RequiredIndicator />
                                            </Label>
                                            <Input
                                                id="old_business_registration_no"
                                                v-model="form.old_identity_no"
                                                :error="form.errors['old_identity_no']"
                                                required
                                                placeholder="Old Business Registration No"
                                            />
                                            <div v-if="form.errors['old_identity_no']"
                                                 class="mt-1 text-sm text-red-500">
                                                {{ form.errors['old_identity_no'] }}
                                            </div>
                                        </div>
                                        <div>
                                            <Label for="date_of_registration" class="text-base"
                                            >Date of Registration
                                                <RequiredIndicator />
                                            </Label>
                                            <Calendar
                                                v-model="form.registration_date"
                                                :error="form.errors['registration_date']"
                                                required
                                                placeholderLabel="Date of Registration"
                                            />
                                            <div v-if="form.errors['registration_date']"
                                                 class="mt-1 text-sm text-red-500">
                                                {{ form.errors['registration_date'] }}
                                            </div>
                                        </div>
                                        <div>
                                            <Label for="years_of_incorporation" class="text-base"
                                            >Years of Incorporation
                                                <RequiredIndicator />
                                            </Label>
                                            {{ form.years_of_incorporation }}
                                        </div>
                                    </template>
                                </div>
                                <div>
                                    <div class="mb-2 flex items-center justify-between pt-3">
                                        <Label class="text-[20px]">Contact Details</Label>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            class="bg-teal hover:bg-teal-hover text-white hover:text-white"
                                            @click="addContact"
                                        >
                                            <FaIcon name="plus" />
                                            Add Contact
                                        </Button>
                                    </div>
                                    <Table class="border">
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead class="w-[500px] border"
                                                >Type
                                                    <RequiredIndicator />
                                                </TableHead>
                                                <TableHead class="w-[500px] border"
                                                >Contact Details
                                                    <RequiredIndicator />
                                                </TableHead>
                                                <TableHead class="w-[100px] border">Able SMS</TableHead>
                                                <TableHead class="w-[100px]"
                                                >Action
                                                    <RequiredIndicator />
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            <TableRow v-for="(contact, index) in form.contacts" :key="index">
                                                <!-- Type Select -->
                                                <TableCell v-if="!contact._delete" class="w-[500px] border">
                                                    <Select v-model="contact.selection_type_id">
                                                        <SelectTrigger class="w-full">
                                                            <SelectValue placeholder="Select type" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem v-for="type in contactTypes" :key="type.id"
                                                                        :value="type.id">
                                                                {{ type.value }}
                                                            </SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                </TableCell>

                                                <!-- Telephone and Country Code -->
                                                <TableCell v-if="!contact._delete" class="w-[500px] border">
                                                    <div v-if="contact.selection_type_id === TELEPHONE_TYPE_ID"
                                                         class="flex">
                                                        <Select v-model="contact.selection_telephone_country_id">
                                                            <SelectTrigger class="bg-cloud w-25 !rounded-r-none">
                                                                <SelectValue placeholder="Select" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem v-for="type in telephoneCountries"
                                                                            :key="type.id" :value="type.id">
                                                                    {{ type.value }}
                                                                </SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                        <Input
                                                            :id="`contacts.${index}.telephone`"
                                                            v-model="contact.telephone"
                                                            placeholder="Contact Number"
                                                            class="w-full !rounded-l-none"
                                                            :error="form.errors[`contacts.${index}.telephone`]"
                                                        />
                                                    </div>
                                                    <div v-else-if="contact.selection_type_id === MOBILE_TYPE_ID"
                                                         class="flex">
                                                        <Select v-model="contact.selection_mobile_country_id">
                                                            <SelectTrigger class="bg-cloud w-25 !rounded-r-none">
                                                                <SelectValue />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem v-for="type in mobileCountries"
                                                                            :key="type.id" :value="type.id">
                                                                    {{ type.value }}
                                                                </SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                        <Input
                                                            :id="`contacts.${index}.mobile`"
                                                            v-model="contact.mobile_phone"
                                                            placeholder="Contact Number"
                                                            class="w-full !rounded-l-none"
                                                        />
                                                    </div>
                                                    <div v-if="form.errors[`contacts.${index}.mobile_phone`]"
                                                         class="mt-1 text-sm text-red-500">
                                                        {{ form.errors[`contacts.${index}.mobile_phone`] }}
                                                    </div>
                                                </TableCell>
                                                <!-- Can Receive SMS -->
                                                <TableCell v-if="!contact._delete"
                                                           class="text-azure w-[100px] border text-center">
                                                    <Checkbox v-model="contact.can_receive_sms" />
                                                </TableCell>

                                                <!-- Is Primary -->
                                                <TableCell v-if="!contact._delete" class="w-[100px] text-center">
                                                    <Badge v-if="contact.is_primary"
                                                           class="text-azure text-md w-15 bg-blue-100 px-1 py-0">
                                                        Primary
                                                    </Badge>
                                                    <Button v-else variant="ghost" size="sm"
                                                            @click="removeContact(index)">
                                                        <FaIcon name="trash" class="text-red" />
                                                    </Button>
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </div>
                                <div class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                                    <div>
                                        <Label for="email" class="text-base">Email </Label>
                                        <Input id="email" v-model="form.email" :error="form.errors.email"
                                               placeholder="Email" />
                                    </div>
                                </div>
                                <div>
                                    <div class="mb-2 flex items-center justify-between">
                                        <Label class="text-[20px]">Address Details</Label>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            class="bg-teal hover:bg-teal-hover text-white hover:text-white"
                                            @click="addAddress"
                                        >
                                            <FaIcon name="plus" />
                                            Add Address
                                        </Button>
                                    </div>
                                    <Accordion type="single" class="w-full" collapsible v-model="openAccordion">
                                        <AccordionItem v-for="(address, index) in form.addresses" :key="index"
                                                       :value="String(index)" class="mb-1">
                                            <Card v-if="!address._delete" class="gap-0 rounded-xs py-0">
                                                <AccordionTrigger
                                                    class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                                    <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                        <FaIcon name="plus" />
                                                    </span>
                                                    <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                        <FaIcon name="minus" />
                                                    </span>
                                                    <span class="flex-1 py-2 text-left font-medium">Address {{ index + 1
                                                        }}</span>
                                                    <template #icon>
                                                        <Button
                                                            type="button"
                                                            @click.stop="removeAddress(index)"
                                                            variant="destructive"
                                                            class="flex items-center gap-1"
                                                            :hidden="index === 0"
                                                        >
                                                            <FaIcon name="trash" />
                                                            Delete
                                                        </Button>
                                                    </template>
                                                </AccordionTrigger>

                                                <Separator />

                                                <AccordionContent class="p-2">
                                                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                        <div class="col-span-2">
                                                            <Label class="text-base"
                                                            >Address Type
                                                                <RequiredIndicator />
                                                            </Label>
                                                            <RadioGroup
                                                                v-model="address.selection_type_id"
                                                                :orientation="'horizontal'"
                                                                class="flex space-x-4"
                                                            >
                                                                <div v-for="address in addressTypes"
                                                                     class="flex items-center space-x-2">
                                                                    <RadioGroupItem :value="address.id" />
                                                                    <Label>{{ address.value }}</Label>
                                                                </div>
                                                            </RadioGroup>
                                                        </div>
                                                        <div class="col-span-2">
                                                            <Label class="text-base"
                                                            >Address Line 1
                                                                <RequiredIndicator />
                                                            </Label>
                                                            <Input v-model="address.line_1"
                                                                   placeholder="Address Line 1" />
                                                            <div v-if="form.errors[`addresses.${index}.line_1`]"
                                                                 class="mt-1 text-sm text-red-500">
                                                                {{ form.errors[`addresses.${index}.line_1`] }}
                                                            </div>
                                                        </div>
                                                        <div class="col-span-2">
                                                            <Label class="text-base">Address Line 2</Label>
                                                            <Input v-model="address.line_2"
                                                                   placeholder="Address Line 2" />
                                                        </div>
                                                        <div>
                                                            <Label class="text-base">Postcode</Label>
                                                            <Input v-model="address.postcode" placeholder="Postcode" />
                                                            <div v-if="form.errors[`addresses.${index}.postcode`]"
                                                                 class="mt-1 text-sm text-red-500">
                                                                {{ form.errors[`addresses.${index}.postcode`] }}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label class="text-base"
                                                            >City
                                                                <RequiredIndicator />
                                                            </Label>
                                                            <Input v-model="address.city" placeholder="City" />
                                                            <div v-if="form.errors[`addresses.${index}.city`]"
                                                                 class="mt-1 text-sm text-red-500">
                                                                {{ form.errors[`addresses.${index}.city`] }}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label class="text-base"
                                                            >State
                                                                <RequiredIndicator />
                                                            </Label>
                                                            <Select v-model="address.selection_state_id">
                                                                <SelectTrigger class="w-full">
                                                                    <SelectValue placeholder="Select state" />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    <SelectItem v-for="state in states" :key="state.id"
                                                                                :value="state.id">
                                                                        {{ state.value }}
                                                                    </SelectItem>
                                                                </SelectContent>
                                                            </Select>
                                                            <div
                                                                v-if="form.errors[`addresses.${index}.selection_state_id`]"
                                                                class="mt-1 text-sm text-red-500"
                                                            >
                                                                {{ form.errors[`addresses.${index}.selection_state_id`]
                                                                }}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <Label class="text-base"
                                                            >Country
                                                                <RequiredIndicator />
                                                            </Label>
                                                            <Select v-model="address.selection_country_id">
                                                                <SelectTrigger class="w-full">
                                                                    <SelectValue placeholder="Select country" />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    <SelectItem v-for="country in countries"
                                                                                :key="country.id" :value="country.id">
                                                                        {{ country.value }}
                                                                    </SelectItem>
                                                                </SelectContent>
                                                            </Select>
                                                            <div
                                                                v-if="form.errors[`addresses.${index}.selection_country_id`]"
                                                                class="mt-1 text-sm text-red-500"
                                                            >
                                                                {{ form.errors[`addresses.${index}.selection_country_id`]
                                                                }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </AccordionContent>
                                            </Card>
                                        </AccordionItem>
                                    </Accordion>
                                </div>
                                <div>
                                    <div>
                                        <Label for="remark" class="text-base">Remark </Label>
                                        <Textarea id="remark" v-model="form.remark" :error="form.errors.remark" required
                                                  placeholder="Remark" />
                                    </div>
                                </div>
                            </CardContent>
                            <CardFooter
                                class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                <Button variant="outline" @click="goToNextTab" type="button"
                                        class="bg-cobalt flex items-center gap-2 text-white">
                                    Next
                                    <FaIcon name="chevron-right" />
                                </Button>
                            </CardFooter>
                        </template>

                        <template #employment>
                            <CardContent class="py-4">
                                <Label class="text-[20px]">Employment Info</Label>
                                <div class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                                    <div>
                                        <Label for="selection_terms_of_employment_id" class="text-base"
                                        >Terms of Employment
                                            <RequiredIndicator />
                                        </Label>
                                        <Select
                                            v-model="form.employment.selection_terms_of_employment_id"
                                            :error="form.errors['employment.selection_terms_of_employment_id']"
                                        >
                                            <SelectTrigger class="w-full">
                                                <SelectValue placeholder="Select Terms of Employment" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="type in termsOfEmploymentTypes" :key="type.id"
                                                            :value="type.id">
                                                    {{ type.value }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <div v-if="form.errors['employment.selection_terms_of_employment_id']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.selection_terms_of_employment_id'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="length_service_year" class="text-base"
                                        >Length of Service (Year)
                                            <RequiredIndicator />
                                        </Label>
                                        <Input
                                            id="length_service_year"
                                            v-model="form.employment.length_service_year"
                                            :error="form.errors['employment.length_service_year']"
                                            required
                                            placeholder="Length of Service (Year)"
                                        />
                                        <div v-if="form.errors['employment.length_service_year']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.length_service_year'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="employer_name" class="text-base"
                                        >Employment Name
                                            <RequiredIndicator />
                                        </Label>
                                        <Input
                                            id="employer_name"
                                            v-model="form.employment.employer_name"
                                            :error="form.errors['employment.employer_name']"
                                            required
                                            placeholder="Employment Name"
                                        />
                                        <div v-if="form.errors['employment.employer_name']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.employer_name'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="length_service_month" class="text-base"
                                        >Length of Service (Month)
                                            <RequiredIndicator />
                                        </Label>
                                        <Input
                                            id="length_service_month"
                                            v-model="form.employment.length_service_month"
                                            :error="form.errors['employment.length_service_year']"
                                            required
                                            placeholder="Length of Service (Month)"
                                        />
                                        <div v-if="form.errors['employment.length_service_year']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.length_service_year'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="job_position" class="text-base"
                                        >Job Position
                                            <RequiredIndicator />
                                        </Label>
                                        <Input
                                            id="job_position"
                                            v-model="form.employment.job_position"
                                            :error="form.errors.job_position"
                                            required
                                            placeholder="Job Position"
                                        />
                                        <div v-if="form.errors['employment.job_position']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.job_position'] }}
                                        </div>
                                    </div>
                                    <div v-if="isSelfEmployed">
                                        <Label for="selection_business_category_id" class="text-base"
                                        >Business Classification
                                            <RequiredIndicator />
                                        </Label>
                                        <Select
                                            v-model="form.employment.selection_business_category_id"
                                            :error="form.errors.selection_business_category_id"
                                        >
                                            <SelectTrigger class="w-full">
                                                <SelectValue placeholder="Select Business Classification" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="type in businessClassificationTypes" :key="type.id"
                                                            :value="type.id">
                                                    {{ type.value }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <div v-if="form.errors['employment.selection_business_category_id']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.selection_business_category_id'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="selection_occupation_id" class="text-base"
                                        >Occupation
                                            <RequiredIndicator />
                                        </Label>
                                        <Select v-model="form.employment.selection_occupation_id"
                                                :error="form.errors.selection_occupation_id">
                                            <SelectTrigger class="w-full">
                                                <SelectValue placeholder="Select Occupation" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="type in occupationsTypes" :key="type.id"
                                                            :value="type.id">
                                                    {{ type.value }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <div v-if="form.errors['employment.selection_occupation_id']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.selection_occupation_id'] }}
                                        </div>
                                    </div>
                                </div>
                                <Label class="py-3 text-[20px]">Income Summary</Label>
                                <div class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                                    <div>
                                        <Label for="gross_income" class="text-base"
                                        >Gross Income (Monthly)
                                            <RequiredIndicator />
                                        </Label>
                                        <Input
                                            id="gross_income"
                                            v-model="form.employment.gross_income"
                                            :error="form.errors.gross_income"
                                            required
                                            placeholder="Gross Income (Monthly)"
                                        />
                                        <div v-if="form.errors['employment.gross_income']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.gross_income'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="net_income" class="text-base"
                                        >Net Income (Monthly)
                                            <RequiredIndicator />
                                        </Label>
                                        <Input
                                            id="net_income"
                                            v-model="form.employment.net_income"
                                            :error="form.errors.net_income"
                                            required
                                            placeholder="Net Income (Monthly)"
                                        />
                                        <div v-if="form.errors['employment.net_income']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.net_income'] }}
                                        </div>
                                    </div>
                                </div>
                                <Label class="py-3 text-[20px]">Office Contact & Address</Label>
                                <div class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                                    <div>
                                        <Label for="employment.telephone" class="text-base">Telephone </Label>
                                        <div class="flex">
                                            <Select v-model="form.employment.selection_telephone_country_id">
                                                <SelectTrigger class="bg-cloud w-25 !rounded-r-none">
                                                    <SelectValue placeholder="Select" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="type in telephoneCountries" :key="type.id"
                                                                :value="type.id">
                                                        {{ type.value }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <Input
                                                :id="`employment.telephone`"
                                                v-model="form.employment.telephone"
                                                :error="form.errors.telephone"
                                                placeholder="Telephone"
                                                class="w-full !rounded-l-none"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="name" class="text-base"
                                        >Mobile Phone
                                            <RequiredIndicator />
                                        </Label>
                                        <div class="flex">
                                            <Select v-model="form.employment.selection_mobile_country_id">
                                                <SelectTrigger class="bg-cloud w-25 !rounded-r-none">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="type in mobileCountries" :key="type.id"
                                                                :value="type.id">
                                                        {{ type.value }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <Input
                                                :id="`employment.mobile`"
                                                v-model="form.employment.mobile_phone"
                                                :error="form.errors.mobile_phone"
                                                placeholder="Mobile Phone"
                                                class="w-full !rounded-l-none"
                                            />
                                        </div>
                                        <div v-if="form.errors['employment.mobile_phone']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.mobile_phone'] }}
                                        </div>
                                    </div>
                                    <div class="col-span-2">
                                        <Label for="employment.address.line_1" class="text-base"
                                        >Address line 1
                                            <RequiredIndicator />
                                        </Label>
                                        <Input
                                            id="employment.address.line_1"
                                            v-model="form.employment.address.line_1"
                                            :error="form.errors.line_1"
                                            required
                                            placeholder="Address line 1"
                                        />
                                        <div v-if="form.errors['employment.address.line_1']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.address.line_1'] }}
                                        </div>
                                    </div>
                                    <div class="col-span-2">
                                        <Label for="employment.address.line_2" class="text-base">Address line 2 </Label>
                                        <Input
                                            id="employment.address.line_2"
                                            v-model="form.employment.address.line_2"
                                            :error="form.errors.line_2"
                                            required
                                            placeholder="Address line 2"
                                        />
                                    </div>
                                    <div>
                                        <Label for="employment.address.postcode" class="text-base"
                                        >Postcode
                                            <RequiredIndicator />
                                        </Label>
                                        <Input
                                            id="employment.address.postcode"
                                            v-model="form.employment.address.postcode"
                                            :error="form.errors.postcode"
                                            required
                                            placeholder="Postcode"
                                        />
                                        <div v-if="form.errors['employment.address.postcode']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.address.postcode'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="employment.address.city" class="text-base"
                                        >City
                                            <RequiredIndicator />
                                        </Label>
                                        <Input
                                            id="employment.address.city"
                                            v-model="form.employment.address.city"
                                            :error="form.errors.city"
                                            required
                                            placeholder="City"
                                        />
                                        <div v-if="form.errors['employment.address.city']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.address.city'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="employment.address.selection_state_id" class="text-base"
                                        >State
                                            <RequiredIndicator />
                                        </Label>
                                        <Select v-model="form.employment.address.selection_state_id"
                                                :error="form.errors.selection_state_id">
                                            <SelectTrigger class="w-full">
                                                <SelectValue placeholder="Select State" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="type in states" :key="type.id" :value="type.id">
                                                    {{ type.value }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <div v-if="form.errors['employment.address.selection_state_id']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.address.selection_state_id'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="employment.address.selection_country_id" class="text-base"
                                        >Country
                                            <RequiredIndicator />
                                        </Label>
                                        <Select v-model="form.employment.address.selection_country_id"
                                                :error="form.errors.selection_country_id">
                                            <SelectTrigger class="w-full">
                                                <SelectValue placeholder="Select Country" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="type in countries" :key="type.id" :value="type.id">
                                                    {{ type.value }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <div v-if="form.errors['employment.address.selection_country_id']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['employment.address.selection_country_id'] }}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                            <CardFooter
                                class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                <Button
                                    variant="outline"
                                    @click="goToPreviousTab"
                                    type="button"
                                    class="bg-card text-muted-foreground flex items-center gap-2"
                                >
                                    <FaIcon name="chevron-left" />
                                    Back
                                </Button>
                                <Button variant="outline" @click="goToNextTab" type="button"
                                        class="bg-cobalt flex items-center gap-2 text-white">
                                    Next
                                    <FaIcon name="chevron-right" />
                                </Button>
                            </CardFooter>
                        </template>

                        <template #company>
                            <CardContent class="py-4">
                                <Label class="text-[20px]" id="company-detail">Company Details</Label>
                                <div class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                                    <div>
                                        <Label for="name" class="text-base">Nature of Business </Label>
                                        <Select
                                            v-model="form.company.selection_nature_of_business_id"
                                            :error="form.errors['company.selection_nature_of_business_id']"
                                        >
                                            <SelectTrigger class="w-full">
                                                <SelectValue placeholder="Select Nature of Business" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="type in naturesOfBusinessTypes" :key="type.id"
                                                            :value="type.id">
                                                    {{ type.value }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <div v-if="form.errors['company.selection_nature_of_business_id']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['company.selection_nature_of_business_id'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="name" class="text-base">Country of Business Operation </Label>
                                        <Select
                                            v-model="form.company.selection_country_of_business_id"
                                            :error="form.errors['company.selection_country_of_business_id']"
                                        >
                                            <SelectTrigger class="w-full">
                                                <SelectValue placeholder="Select Country of Business Operation" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="type in countries" :key="type.id" :value="type.id">
                                                    {{ type.value }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <div v-if="form.errors['company.selection_country_of_business_id']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['company.selection_country_of_business_id'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="current_paid_up_capital" class="text-base">Current Paid Up Capital
                                            (RM) </Label>
                                        <Input
                                            id="current_paid_up_capital"
                                            v-model="form.company.current_paid_up_capital"
                                            :error="form.errors['company.current_paid_up_capital']"
                                            required
                                            placeholder="Current Paid Up Capital (RM)"
                                        />
                                        <div v-if="form.errors['company.current_paid_up_capital']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['company.current_paid_up_capital'] }}
                                        </div>
                                    </div>
                                    <div></div>
                                    <div>
                                        <Label for="business_turnover" class="text-base">Business Turnover (RM) </Label>
                                        <Input
                                            id="business_turnover"
                                            v-model="form.company.business_turnover"
                                            :error="form.errors.business_turnover"
                                            required
                                            placeholder="Business Turnover (RM)"
                                            class="mb-2"
                                        />
                                        <div v-if="form.errors['company.business_turnover']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['company.business_turnover'] }}
                                        </div>
                                        <Calendar
                                            v-model="form.company.business_turnover_date"
                                            disableFuture
                                            :error="form.errors.business_turnover_date"
                                            placeholderLabel="Last Audit Date"
                                        />
                                        <div v-if="form.errors['company.business_turnover_date']"
                                             class="mt-1 text-sm text-red-500">
                                            {{ form.errors['company.business_turnover_date'] }}
                                        </div>
                                    </div>
                                    <div>
                                        <Label for="business_net_income" class="text-base">Business Net Income
                                            (RM) </Label>
                                        <Input
                                            id="business_net_income"
                                            v-model="form.company.business_net_income"
                                            :error="form.errors.business_net_income"
                                            required
                                            placeholder="Business Net Income (RM)"
                                            class="mb-2"
                                        />
                                        <Calendar
                                            v-model="form.company.business_net_income_date"
                                            disableFuture
                                            :error="form.errors.business_net_income_date"
                                            placeholderLabel="Last Audit Date"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <div class="mb-2 flex items-center justify-between pt-3">
                                        <Label class="text-[20px]">Owner Details</Label>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            class="bg-teal hover:bg-teal-hover text-white hover:text-white"
                                            @click="addOwner"
                                        >
                                            <FaIcon name="plus" />
                                            Add Owner
                                        </Button>
                                    </div>
                                    <Accordion type="single" class="w-full" collapsible v-model="openAccordionIndex">
                                        <AccordionItem v-for="(owner, index) in form.owners" :key="index"
                                                       :value="String(index)" class="mb-1">
                                            <Card v-if="!owner._delete" class="gap-0 rounded-xs py-0">
                                                <AccordionTrigger
                                                    class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                                    <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                        <FaIcon name="plus" />
                                                    </span>
                                                    <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                        <FaIcon name="minus" />
                                                    </span>
                                                    <span class="flex-1 py-2 text-left font-medium"> Owner {{ index + 1
                                                        }} </span>
                                                    <template #icon>
                                                        <Button
                                                            type="button"
                                                            @click.stop="removeOwner(index)"
                                                            variant="destructive"
                                                            class="flex items-center gap-1"
                                                        >
                                                            <FaIcon name="trash" />
                                                            Delete
                                                        </Button>
                                                    </template>
                                                </AccordionTrigger>

                                                <Separator />
                                                <AccordionContent class="p-2">
                                                    <div>
                                                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                            <div class="col-span-2">
                                                                <Label class="py-2 text-base"
                                                                >Owner Type
                                                                    <RequiredIndicator />
                                                                </Label>
                                                                <RadioGroup
                                                                    v-model="owner.selection_type_id"
                                                                    :orientation="'horizontal'"
                                                                    class="flex space-x-4"
                                                                >
                                                                    <div v-for="owner in ownerTypes"
                                                                         class="flex items-center space-x-2">
                                                                        <RadioGroupItem :value="owner.id" />
                                                                        <Label>{{ owner.value }}</Label>
                                                                    </div>
                                                                </RadioGroup>
                                                            </div>
                                                            <div>
                                                                <Label for="" class="text-base"
                                                                >Name
                                                                    <RequiredIndicator />
                                                                </Label>
                                                                <Input id="name" v-model="owner.name" required
                                                                       placeholder="Name" />
                                                                <div v-if="form.errors[`owners.${index}.name`]"
                                                                     class="mt-1 text-sm text-red-500">
                                                                    {{ form.errors[`owners.${index}.name`] }}
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <Label for="" class="text-base"
                                                                >IC / Passport
                                                                    <RequiredIndicator />
                                                                </Label>
                                                                <Input
                                                                    id="company_name"
                                                                    v-model="owner.identity_no"
                                                                    required
                                                                    placeholder="IC / Passport"
                                                                />
                                                                <div
                                                                    v-if="form.errors[`owners.${index}.identity_no`]"
                                                                    class="mt-1 text-sm text-red-500"
                                                                >
                                                                    {{ form.errors[`owners.${index}.identity_no`] }}
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <Label for="" class="text-base"
                                                                >Nationality
                                                                    <RequiredIndicator />
                                                                </Label>
                                                                <Select v-model="owner.selection_nationality_id">
                                                                    <SelectTrigger class="w-full">
                                                                        <SelectValue placeholder="Select Nationality" />
                                                                    </SelectTrigger>
                                                                    <SelectContent>
                                                                        <SelectItem v-for="type in nationalities"
                                                                                    :key="type.id" :value="type.id">
                                                                            {{ type.value }}
                                                                        </SelectItem>
                                                                    </SelectContent>
                                                                </Select>
                                                                <div
                                                                    v-if="form.errors[`owners.${index}.selection_nationality_id`]"
                                                                    class="mt-1 text-sm text-red-500"
                                                                >
                                                                    {{ form.errors[`owners.${index}.selection_nationality_id`]
                                                                    }}
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <Label for="" class="text-base"
                                                                >Share (%)
                                                                    <RequiredIndicator />
                                                                </Label>
                                                                <Input
                                                                    id="share_unit"
                                                                    v-model="owner.share_unit"
                                                                    required
                                                                    type="number"
                                                                    step="0.01"
                                                                    min="0"
                                                                    placeholder="Share (%)"
                                                                />
                                                                <div
                                                                    v-if="form.errors[`owners.${index}.share_unit`]"
                                                                    class="mt-1 text-sm text-red-500"
                                                                >
                                                                    {{ form.errors[`owners.${index}.share_unit`] }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </AccordionContent>
                                            </Card>
                                        </AccordionItem>
                                    </Accordion>
                                </div>
                            </CardContent>
                            <CardFooter
                                class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                <Button
                                    variant="outline"
                                    @click="goToPreviousTab"
                                    type="button"
                                    class="bg-card text-muted-foreground flex items-center gap-2"
                                >
                                    <FaIcon name="chevron-left" />
                                    Back
                                </Button>
                                <Button variant="outline" @click="goToNextTab" type="button"
                                        class="bg-cobalt flex items-center gap-2 text-white">
                                    Next
                                    <FaIcon name="chevron-right" />
                                </Button>
                            </CardFooter>
                        </template>

                        <template #collateral>
                            <CardContent class="py-4">
                                <Collateral
                                    :form="form.collateral"
                                    :addCollateral="addCollateral"
                                    :removeCollateral="removeCollateral"
                                    :errors="form.errors"
                                    :customerTypes="customerTypes"
                                    :collateralTypes="collateralTypes"
                                    :propertyTypes="propertyTypes"
                                    :landCategories="landCategories"
                                    :landStatuses="landStatuses"
                                    :squareTypes="squareTypes"
                                    :states="states"
                                    :countries="countries"
                                    :mobileCountries="mobileCountries"
                                    :telephoneCountries="telephoneCountries"
                                    :ordinal="ordinal"
                                    isAccordion
                                    isEdit
                                />
                            </CardContent>
                            <CardFooter
                                class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                <Button
                                    variant="outline"
                                    @click="goToPreviousTab"
                                    type="button"
                                    class="bg-card text-muted-foreground flex items-center gap-2"
                                >
                                    <FaIcon name="chevron-left" />
                                    Back
                                </Button>
                                <Button variant="outline" @click="goToNextTab" type="button"
                                        class="bg-cobalt flex items-center gap-2 text-white">
                                    Next
                                    <FaIcon name="chevron-right" />
                                </Button>
                            </CardFooter>
                        </template>

                        <template #document>
                            <CardContent class="py-4">
                                <div class="flex gap-4">
                                    <!-- Left section (60%) -->
                                    <div class="flex w-[75%] flex-col gap-2">
                                        <Accordion type="single" class="w-full" collapsible>
                                            <AccordionItem value="item-1" class="mb-1">
                                                <Card class="gap-0 rounded-lg py-0">
                                                    <AccordionTrigger
                                                        class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline"
                                                    >
                                                        <template #icon>
                                                            <span
                                                                class="bg-cloud text-pale flex w-full items-center justify-center gap-1 p-1.5 group-data-[state=open]:hidden"
                                                            >
                                                                <FaIcon name="plus" />
                                                                Upload
                                                            </span>
                                                            <span
                                                                class="bg-cloud text-pale flex hidden w-full items-center justify-center gap-1 p-1.5 group-data-[state=open]:flex"
                                                            >
                                                                <FaIcon name="minus" />
                                                                Hide
                                                            </span>
                                                        </template>
                                                    </AccordionTrigger>
                                                    <AccordionContent class="p-2">
                                                        <div>
                                                            <Label
                                                                for="document"
                                                                class="flex h-[150px] w-full cursor-pointer flex-col items-center justify-center rounded-md border-3 border-dashed text-center text-gray-500 transition hover:bg-gray-50"
                                                            >
                                                                <span class="text-lavender text-[20px]">
                                                                    <FaIcon name="cloud-arrow-up" />
                                                                </span>
                                                                <span
                                                                    class="text-sm">Drag & Drop or Click to Browse</span>
                                                                <span
                                                                    class="text- text-xs">Max: 10MB (PDF, JPEG, PNG)</span>
                                                                <Input id="document" type="file" multiple class="hidden"
                                                                       @change="handleFileUpload" />
                                                            </Label>
                                                        </div>
                                                        <div v-if="uploadedFiles.length" class="mt-2 space-y-1 text-sm">
                                                            <div
                                                                v-for="(entry, index) in uploadedFiles"
                                                                :key="index"
                                                                class="bg-cloud mb-2 grid grid-cols-[40px_1fr_200px_40px_40px] items-center gap-2 rounded border p-2"
                                                            >
                                                                <div class="flex justify-center text-blue-500">
                                                                    <template
                                                                        v-if="entry.file.name.startsWith('image/')">
                                                                        <img
                                                                            :src="getPreviewUrl(entry.file)"
                                                                            alt="preview"
                                                                            class="h-full w-full object-cover"
                                                                        />
                                                                    </template>
                                                                    <template v-else>
                                                                        <span class="text-lg"><FaIcon
                                                                            name="file" /></span>
                                                                    </template>
                                                                </div>
                                                                <div class="flex flex-col truncate">
                                                                    <span
                                                                        class="truncate font-medium">{{ entry.file.name
                                                                        }}</span>
                                                                    <span class="text-xs text-gray-400"
                                                                    >{{ (entry.file.size / 1024).toFixed(1) }} KB</span
                                                                    >
                                                                </div>
                                                                <Select
                                                                    v-model="uploadedFiles[index].selection_type_id">
                                                                    <SelectTrigger class="w-full bg-white">
                                                                        <SelectValue
                                                                            placeholder="Select Document Type" />
                                                                    </SelectTrigger>
                                                                    <SelectContent>
                                                                        <SelectItem v-for="type in documentTypes"
                                                                                    :key="type.id" :value="type.id">
                                                                            {{ type.value }}
                                                                        </SelectItem>
                                                                    </SelectContent>
                                                                </Select>
                                                                <button
                                                                    type="button"
                                                                    @click="removeFile(index)"
                                                                    class="text-destructive flex justify-center"
                                                                >
                                                                    <FaIcon name="trash" />
                                                                </button>
                                                                <button
                                                                    type="button"
                                                                    @click="viewFile(entry.file)"
                                                                    class="text-azure flex justify-center"
                                                                >
                                                                    <FaIcon name="eye" />
                                                                </button>
                                                            </div>
                                                            <div
                                                                class="mb-0 flex items-center justify-end gap-2 rounded-b-xl px-0 py-1">
                                                                <Button
                                                                    type="button"
                                                                    variant="outline"
                                                                    class="bg-card text-muted-foreground flex items-center gap-2"
                                                                    @click="
                                                                        () => {
                                                                            uploadedFiles = [];
                                                                        }
                                                                    "
                                                                >Clear
                                                                </Button>
                                                                <Button type="button" class="bg-greenish text-white"
                                                                        @click="uploadFiles"
                                                                >Upload
                                                                </Button>
                                                            </div>
                                                        </div>
                                                    </AccordionContent>
                                                </Card>
                                            </AccordionItem>
                                        </Accordion>

                                        <Card class="h-[420px] w-full rounded-lg py-2">
                                            <CardContent class="px-2">
                                                <TabsWrapper v-model="activeDocTab" :tabs="docItems">
                                                    <template #all>
                                                        <CardContent class="px-0">
                                                            <div
                                                                v-if="categorizedFiles['all'].length == 0"
                                                                class="flex h-[380px] items-center justify-center"
                                                            >
                                                                <p>No Documents</p>
                                                            </div>
                                                            <div
                                                                v-else
                                                                class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                            >
                                                                <div
                                                                    v-for="(doc, index) in categorizedFiles['all']"
                                                                    :key="doc.id"
                                                                    @click="toggleSelect(doc.id)"
                                                                    :class="[
                                                                        'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                        selectedDocIds.includes(doc.id)
                                                                            ? 'border-blue-500 bg-blue-50'
                                                                            : 'border-transparent hover:border-gray-300',
                                                                    ]"
                                                                >
                                                                    <div
                                                                        v-if="selectedDocIds.includes(doc.id)"
                                                                        class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                    >
                                                                        <span class="text-xs">
                                                                            <FaIcon name="check" class="text-white" />
                                                                        </span>
                                                                    </div>
                                                                    <template v-if="isImage(doc.name)">
                                                                        <img :src="doc.url" alt="preview"
                                                                             class="h-[85px] w-[150px] object-contain" />
                                                                    </template>
                                                                    <template v-else>
                                                                        <span class="text-[35px]"
                                                                        ><FaIcon
                                                                            name="file-pdf"
                                                                            class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                        /></span>
                                                                    </template>
                                                                    <a
                                                                        :href="doc.url"
                                                                        target="_blank"
                                                                        class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                    >
                                                                        {{ doc.name }}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </CardContent>
                                                    </template>

                                                    <template #customer-doc>
                                                        <CardContent class="px-0">
                                                            <div
                                                                v-if="categorizedFiles['customer-doc'].length == 0"
                                                                class="flex h-[380px] items-center justify-center"
                                                            >
                                                                <p>No Documents</p>
                                                            </div>
                                                            <div
                                                                v-else
                                                                class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                            >
                                                                <div
                                                                    v-for="(doc, index) in categorizedFiles['customer-doc']"
                                                                    :key="doc.id"
                                                                    @click="toggleSelect(doc.id)"
                                                                    :class="[
                                                                        'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                        selectedDocIds.includes(doc.id)
                                                                            ? 'border-blue-500 bg-blue-50'
                                                                            : 'border-transparent hover:border-gray-300',
                                                                    ]"
                                                                >
                                                                    <div
                                                                        v-if="selectedDocIds.includes(doc.id)"
                                                                        class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                    >
                                                                        <span class="text-xs">
                                                                            <FaIcon name="check" class="text-white" />
                                                                        </span>
                                                                    </div>
                                                                    <template v-if="isImage(doc.name)">
                                                                        <img :src="doc.url" alt="preview"
                                                                             class="h-[85px] w-[150px] object-contain" />
                                                                    </template>
                                                                    <template v-else>
                                                                        <span class="text-[35px]"
                                                                        ><FaIcon
                                                                            name="file-pdf"
                                                                            class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                        /></span>
                                                                    </template>
                                                                    <a
                                                                        :href="doc.url"
                                                                        target="_blank"
                                                                        class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                    >
                                                                        {{ doc.name }}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </CardContent>
                                                    </template>

                                                    <template #collateral-doc>
                                                        <CardContent class="px-0">
                                                            <div
                                                                v-if="categorizedFiles['collateral-doc'].length == 0"
                                                                class="flex h-[380px] items-center justify-center"
                                                            >
                                                                <p>No Documents</p>
                                                            </div>
                                                            <div
                                                                v-else
                                                                class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                            >
                                                                <div
                                                                    v-for="(doc, index) in categorizedFiles['collateral-doc']"
                                                                    :key="doc.id"
                                                                    @click="toggleSelect(doc.id)"
                                                                    :class="[
                                                                        'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                        selectedDocIds.includes(doc.id)
                                                                            ? 'border-blue-500 bg-blue-50'
                                                                            : 'border-transparent hover:border-gray-300',
                                                                    ]"
                                                                >
                                                                    <div
                                                                        v-if="selectedDocIds.includes(doc.id)"
                                                                        class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                    >
                                                                        <span class="text-xs">
                                                                            <FaIcon name="check" class="text-white" />
                                                                        </span>
                                                                    </div>
                                                                    <template v-if="isImage(doc.name)">
                                                                        <img :src="doc.url" alt="preview"
                                                                             class="h-[85px] w-[150px] object-contain" />
                                                                    </template>
                                                                    <template v-else>
                                                                        <span class="text-[35px]"
                                                                        ><FaIcon
                                                                            name="file-pdf"
                                                                            class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                        /></span>
                                                                    </template>
                                                                    <a
                                                                        :href="doc.url"
                                                                        target="_blank"
                                                                        class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                    >
                                                                        {{ doc.name }}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </CardContent>
                                                    </template>

                                                    <template #security-doc>
                                                        <CardContent class="px-0">
                                                            <div
                                                                v-if="categorizedFiles['security-doc'].length == 0"
                                                                class="flex h-[380px] items-center justify-center"
                                                            >
                                                                <p>No Documents</p>
                                                            </div>
                                                            <div
                                                                v-else
                                                                class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                            >
                                                                <div
                                                                    v-for="(doc, index) in categorizedFiles['security-doc']"
                                                                    :key="doc.id"
                                                                    @click="toggleSelect(doc.id)"
                                                                    :class="[
                                                                        'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                        selectedDocIds.includes(doc.id)
                                                                            ? 'border-blue-500 bg-blue-50'
                                                                            : 'border-transparent hover:border-gray-300',
                                                                    ]"
                                                                >
                                                                    <div
                                                                        v-if="selectedDocIds.includes(doc.id)"
                                                                        class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                    >
                                                                        <span class="text-xs">
                                                                            <FaIcon name="check" class="text-white" />
                                                                        </span>
                                                                    </div>
                                                                    <template v-if="isImage(doc.name)">
                                                                        <img :src="doc.url" alt="preview"
                                                                             class="h-[85px] w-[150px] object-contain" />
                                                                    </template>
                                                                    <template v-else>
                                                                        <span class="text-[35px]"
                                                                        ><FaIcon
                                                                            name="file-pdf"
                                                                            class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                        /></span>
                                                                    </template>
                                                                    <a
                                                                        :href="doc.url"
                                                                        target="_blank"
                                                                        class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                    >
                                                                        {{ doc.name }}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </CardContent>
                                                    </template>
                                                </TabsWrapper>
                                            </CardContent>
                                        </Card>
                                        <CardFooter
                                            class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-1"
                                            v-if="selectedDocIds.length > 0"
                                        >
                                            <button
                                                @click="removeSelectedDocs"
                                                class="rounded bg-red-600 px-4 py-2 text-white transition hover:bg-red-700"
                                            >
                                                Remove
                                            </button>
                                        </CardFooter>
                                    </div>
                                    <div class="w-[25%]">
                                        <!-- TODO: show selected doc info -->
                                        <Card class="flex h-[500px]" v-if="selectedDocIds.length > 0">
                                            <CardContent>
                                                <template v-if="selectedDocIds.length === 1 && selectedFile">
                                                    <div class="mt-4 w-full max-w-md">
                                                        <!-- File name as link -->
                                                        <a
                                                            :href="selectedFile.url"
                                                            target="_blank"
                                                            class="mb-3 block truncate text-sm text-blue-700 hover:underline"
                                                        >
                                                            {{ selectedFile.name }}
                                                        </a>

                                                        <!-- Icon -->
                                                        <div v-if="isImage(selectedFile.name)" class="mb-6 text-center">
                                                            <img
                                                                :src="selectedFile.url"
                                                                alt="preview"
                                                                class="h-auto max-h-[150px] w-full object-contain"
                                                            />
                                                        </div>
                                                        <div v-else class="mb-6 text-center">
                                                            <span class="text-lavender text-[50px]">
                                                                <FaIcon name="file" />
                                                            </span>
                                                        </div>

                                                        <!-- Details -->
                                                        <div class="space-y-4 text-sm">
                                                            <div>
                                                                <p class="font-semibold">File Type:</p>
                                                                <p>{{ selectedFile.type }}</p>
                                                            </div>
                                                            <div>
                                                                <p class="font-semibold">File Size:</p>
                                                                <p>{{ (selectedFile.size / 1024).toFixed(2) }} KB</p>
                                                            </div>
                                                            <div>
                                                                <p class="font-semibold">Created Date:</p>
                                                                <p>{{ selectedFile.createdAt }}</p>
                                                            </div>
                                                            <div>
                                                                <p class="font-semibold">Uploaded By:</p>
                                                                <p>{{ selectedFile.uploadedBy }}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                                <template v-else>
                                                    <div class="text-center">
                                                        <span class="text-lavender text-[50px]">
                                                            <FaIcon name="file" />
                                                        </span>
                                                    </div>
                                                    <p class="text-center">{{ selectedDocIds.length }} items
                                                        selected.</p>
                                                </template>
                                            </CardContent>
                                        </Card>
                                        <Card v-else class="flex h-[500px] items-center justify-center">
                                            <CardContent>
                                                <div class="text-center">
                                                    <span class="text-lavender text-[50px]">
                                                        <FaIcon name="file" />
                                                    </span>
                                                    <p>0 Item</p>
                                                    <p>Select a single file to get more information</p>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    </div>
                                </div>
                            </CardContent>
                            <CardFooter class="bg-stone flex justify-end gap-2 px-4 py-3">
                                <Button
                                    variant="outline"
                                    @click="goToPreviousTab"
                                    type="button"
                                    class="bg-card text-muted-foreground flex items-center gap-2"
                                >
                                    <FaIcon name="chevron-left" />
                                    Back
                                </Button>
                                <Button type="button" @click="submit"
                                        class="bg-green flex items-center gap-2 text-white">
                                    Submit
                                    <FaIcon name="paper-plane" />
                                </Button>
                            </CardFooter>
                        </template>
                    </TabsWrapper>
                </Card>
            </form>
        </div>
    </AppLayout>
</template>
